<template>
  <nav 
    dir="rtl" 
    class="sticky top-0 z-50 glass-surface border-b border-white/20"
    aria-label="ناوبری اصلی"
  >
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex items-center justify-between h-16">
        
        <!-- <PERSON><PERSON> and <PERSON> (Right side in RTL) -->
        <div class="flex items-center gap-3">
          <div class="flex-shrink-0">
            <div class="w-10 h-10 bg-gradient-primary rounded-xl flex items-center justify-center">
              <BookOpenIcon class="w-6 h-6 text-white" />
            </div>
          </div>
          <div class="hidden md:block">
            <h1 class="text-xl font-bold text-slate-900">ScholarHub</h1>
            <p class="text-xs text-slate-600">پایگاه داده مقالات علمی</p>
          </div>
        </div>

        <!-- Desktop Navigation Links (Center) -->
        <div class="hidden md:flex items-center gap-8">
          <a 
            href="#" 
            class="text-slate-700 hover:text-primary-600 font-medium transition-colors focus-ring rounded-lg px-3 py-2"
            aria-current="page"
          >
            صفحه اصلی
          </a>
          <a 
            href="#" 
            class="text-slate-700 hover:text-primary-600 font-medium transition-colors focus-ring rounded-lg px-3 py-2"
          >
            جستجوی پیشرفته
          </a>
          <a 
            href="#" 
            class="text-slate-700 hover:text-primary-600 font-medium transition-colors focus-ring rounded-lg px-3 py-2"
          >
            پژوهشگران
          </a>
          <a 
            href="#" 
            class="text-slate-700 hover:text-primary-600 font-medium transition-colors focus-ring rounded-lg px-3 py-2"
          >
            مجلات
          </a>
        </div>

        <!-- Right side actions (Left side in RTL) -->
        <div class="flex items-center gap-4">
          
          <!-- Language Switcher -->
          <div class="relative">
            <button
              @click="toggleLanguageMenu"
              class="flex items-center gap-2 text-slate-700 hover:text-primary-600 focus-ring rounded-lg px-3 py-2"
              :aria-expanded="showLanguageMenu"
              aria-haspopup="true"
            >
              <GlobeAltIcon class="w-5 h-5" />
              <span class="hidden sm:inline">فارسی</span>
              <ChevronDownIcon class="w-4 h-4" />
            </button>
            
            <!-- Language Dropdown -->
            <div 
              v-show="showLanguageMenu"
              class="absolute left-0 mt-2 w-48 neo-surface rounded-xl shadow-lg py-2 z-50"
              role="menu"
            >
              <button 
                class="w-full text-right px-4 py-2 text-slate-700 hover:bg-slate-50 focus:bg-slate-50 focus-ring"
                role="menuitem"
              >
                🇮🇷 فارسی
              </button>
              <button 
                class="w-full text-right px-4 py-2 text-slate-700 hover:bg-slate-50 focus:bg-slate-50 focus-ring"
                role="menuitem"
              >
                🇺🇸 English
              </button>
            </div>
          </div>

          <!-- Accessibility Menu -->
          <button
            @click="toggleAccessibilityMenu"
            class="p-2 text-slate-700 hover:text-primary-600 focus-ring rounded-lg"
            :aria-expanded="showAccessibilityMenu"
            aria-label="منوی دسترسی‌پذیری"
            title="تنظیمات دسترسی‌پذیری"
          >
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2a10 10 0 1 0 10 10A10 10 0 0 0 12 2zm0 18a8 8 0 1 1 8-8 8 8 0 0 1-8 8z"/>
              <path d="M12 6a1.5 1.5 0 1 0 1.5 1.5A1.5 1.5 0 0 0 12 6zm-1.5 4.5h3v7h-3z"/>
            </svg>
          </button>

          <!-- Accessibility Dropdown -->
          <div 
            v-show="showAccessibilityMenu"
            class="absolute left-4 mt-12 w-64 neo-surface rounded-xl shadow-lg p-4 z-50"
            role="menu"
          >
            <h3 class="font-semibold text-slate-900 mb-3 text-right">تنظیمات دسترسی‌پذیری</h3>
            <div class="space-y-3">
              <label class="flex items-center justify-between">
                <span class="text-sm text-slate-700">افزایش اندازه متن</span>
                <input type="checkbox" class="rounded focus-ring">
              </label>
              <label class="flex items-center justify-between">
                <span class="text-sm text-slate-700">حالت کنتراست بالا</span>
                <input type="checkbox" class="rounded focus-ring">
              </label>
              <label class="flex items-center justify-between">
                <span class="text-sm text-slate-700">کاهش انیمیشن‌ها</span>
                <input type="checkbox" class="rounded focus-ring">
              </label>
            </div>
          </div>

          <!-- User Actions -->
          <div class="flex items-center gap-2">
            <button class="btn-secondary text-sm px-4 py-2">
              ورود
            </button>
            <button class="btn-primary text-sm px-4 py-2">
              ثبت‌نام
            </button>
          </div>

          <!-- Mobile Menu Button -->
          <button
            @click="toggleMobileMenu"
            class="md:hidden p-2 text-slate-700 hover:text-primary-600 focus-ring rounded-lg"
            :aria-expanded="showMobileMenu"
            aria-label="منوی موبایل"
          >
            <Bars3Icon v-if="!showMobileMenu" class="w-6 h-6" />
            <XMarkIcon v-else class="w-6 h-6" />
          </button>
        </div>
      </div>

      <!-- Mobile Menu -->
      <div 
        v-show="showMobileMenu"
        class="md:hidden border-t border-white/20 py-4 space-y-2"
      >
        <a href="#" class="block px-4 py-2 text-slate-700 hover:bg-slate-50 rounded-lg">صفحه اصلی</a>
        <a href="#" class="block px-4 py-2 text-slate-700 hover:bg-slate-50 rounded-lg">جستجوی پیشرفته</a>
        <a href="#" class="block px-4 py-2 text-slate-700 hover:bg-slate-50 rounded-lg">پژوهشگران</a>
        <a href="#" class="block px-4 py-2 text-slate-700 hover:bg-slate-50 rounded-lg">مجلات</a>
      </div>
    </div>
  </nav>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { 
  BookOpenIcon, 
  GlobeAltIcon, 
  ChevronDownIcon,
  Bars3Icon,
  XMarkIcon
} from '@heroicons/vue/24/outline'

// Reactive state
const showLanguageMenu = ref(false)
const showAccessibilityMenu = ref(false)
const showMobileMenu = ref(false)

// Toggle functions
const toggleLanguageMenu = () => {
  showLanguageMenu.value = !showLanguageMenu.value
  showAccessibilityMenu.value = false
}

const toggleAccessibilityMenu = () => {
  showAccessibilityMenu.value = !showAccessibilityMenu.value
  showLanguageMenu.value = false
}

const toggleMobileMenu = () => {
  showMobileMenu.value = !showMobileMenu.value
}

// Close menus when clicking outside
const closeMenus = (event) => {
  if (!event.target.closest('.relative')) {
    showLanguageMenu.value = false
    showAccessibilityMenu.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', closeMenus)
})

onUnmounted(() => {
  document.removeEventListener('click', closeMenus)
})
</script>
