@tailwind base;
@tailwind components;
@tailwind utilities;

/* Persian/RTL Base Styles */
@layer base {
  html {
    font-family: 'Vazirmatn', 'Vazir', 'IRANSans', system-ui, -apple-system, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  }
  
  html[dir="rtl"] {
    direction: rtl;
  }
  
  body {
    @apply bg-slate-50 text-slate-900 antialiased;
  }
  
  /* Persian numerals utility */
  .persian-numerals {
    font-feature-settings: "ss01" on;
  }
  
  /* Ensure proper text alignment for RTL */
  .rtl-text {
    @apply text-right;
  }
  
  /* Focus styles for accessibility */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }
  
  /* Smooth transitions */
  * {
    @apply transition-colors duration-200;
  }
}

/* Component Layer - Neomorphism and Custom Components */
@layer components {
  /* Neomorphism Utilities */
  .neo-surface {
    @apply bg-white/60 backdrop-blur-sm shadow-neo;
  }
  
  .neo-button {
    @apply bg-white/80 backdrop-blur-sm shadow-neo hover:shadow-neo-lg active:shadow-neo-inset;
    @apply transition-all duration-200 ease-out;
  }
  
  .neo-input {
    @apply bg-white/40 backdrop-blur-xs shadow-neo-inset border-0;
    @apply focus:shadow-neo focus:bg-white/60;
    @apply transition-all duration-200;
  }
  
  .neo-card {
    @apply bg-white/70 backdrop-blur-sm shadow-neo rounded-xl;
    @apply hover:shadow-neo-lg hover:bg-white/80;
    @apply transition-all duration-300 ease-out;
  }
  
  /* Glass Effect */
  .glass-surface {
    @apply bg-white/60 backdrop-blur-sm border border-white/20;
    box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
  }
  
  /* Primary Button */
  .btn-primary {
    @apply bg-primary-500 text-white px-6 py-3 rounded-xl font-medium;
    @apply hover:bg-primary-600 active:bg-primary-700;
    @apply shadow-lg hover:shadow-xl;
    @apply focus-ring;
    @apply transition-all duration-200;
  }
  
  /* Secondary Button */
  .btn-secondary {
    @apply neo-button text-slate-700 px-6 py-3 rounded-xl font-medium;
    @apply hover:text-slate-900;
    @apply focus-ring;
  }
  
  /* Filter Chip */
  .filter-chip {
    @apply px-4 py-2 rounded-full text-sm font-medium;
    @apply bg-slate-100 text-slate-700 hover:bg-slate-200;
    @apply focus-ring;
    @apply transition-all duration-200;
  }
  
  .filter-chip.active {
    @apply bg-primary-500 text-white shadow-lg;
  }
  
  /* Search Input */
  .search-input {
    @apply w-full bg-transparent text-lg placeholder:text-slate-400;
    @apply text-slate-900 text-right border-0 focus:outline-none;
    @apply py-4 px-6;
  }
  
  /* Card Hover Effects */
  .hover-lift {
    @apply transform hover:-translate-y-1 hover:scale-[1.02];
    @apply transition-transform duration-300 ease-out;
  }
  
  /* Persian Text Styles */
  .text-persian-h1 {
    @apply text-3xl lg:text-4xl font-semibold leading-tight text-right;
  }
  
  .text-persian-h2 {
    @apply text-2xl lg:text-3xl font-semibold leading-tight text-right;
  }
  
  .text-persian-h3 {
    @apply text-xl lg:text-2xl font-medium leading-tight text-right;
  }
  
  .text-persian-body {
    @apply text-base leading-relaxed text-right;
  }
  
  .text-persian-small {
    @apply text-sm leading-relaxed text-right;
  }
  
  /* Loading States */
  .loading-shimmer {
    @apply animate-pulse bg-gradient-to-r from-slate-200 via-slate-100 to-slate-200;
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
  }
  
  @keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
  }
  
  /* Accessibility Helpers */
  .sr-only-focusable {
    @apply sr-only focus:not-sr-only focus:absolute focus:z-50;
    @apply focus:p-2 focus:bg-white focus:text-slate-900;
    @apply focus:border focus:border-primary-500;
  }
  
  /* Mobile Bottom Navigation */
  .mobile-nav {
    @apply fixed bottom-0 left-0 right-0 z-50;
    @apply bg-white/90 backdrop-blur-md border-t border-slate-200;
    @apply safe-area-inset-bottom;
    padding-bottom: env(safe-area-inset-bottom);
  }
  
  /* Chart Container */
  .chart-container {
    @apply neo-card p-6;
    @apply relative overflow-hidden;
  }
  
  /* Gradient Backgrounds */
  .gradient-primary {
    background: linear-gradient(135deg, #125CC8 0%, #0ea5e9 100%);
  }
  
  .gradient-accent {
    background: linear-gradient(135deg, #0ea5e9 0%, #3b82f6 100%);
  }
  
  /* Persian Date Styling */
  .persian-date {
    @apply text-sm text-slate-500 font-medium;
    font-feature-settings: "ss01" on; /* Enable Persian numerals if supported */
  }
  
  /* Notification Badge */
  .notification-badge {
    @apply absolute -top-1 -right-1 bg-danger text-white text-xs;
    @apply rounded-full w-5 h-5 flex items-center justify-center;
    @apply font-medium;
  }
}
