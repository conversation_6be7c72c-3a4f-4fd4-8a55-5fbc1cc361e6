{"name": "scholarhub-homepage", "version": "1.0.0", "description": "RTL Persian Scientific Paper Database Homepage - Vue 3 + TailwindCSS", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"vue": "^3.4.0", "@headlessui/vue": "^1.7.16", "@heroicons/vue": "^2.0.18", "chart.js": "^4.4.0", "vue-chartjs": "^5.3.0", "dayjs": "^1.11.10", "dayjs-jalali": "^1.0.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.0", "vite": "^5.0.0", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss-rtl": "^0.9.0", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "eslint": "^8.54.0", "eslint-plugin-vue": "^9.18.1"}}