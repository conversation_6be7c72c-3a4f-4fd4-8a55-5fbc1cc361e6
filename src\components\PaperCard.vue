<template>
  <article 
    class="neo-card p-6 hover-lift group relative"
    :class="{ 'ring-2 ring-primary-200': isSelected }"
    dir="rtl"
  >
    <!-- Header -->
    <div class="flex items-start justify-between gap-4 mb-4">
      <div class="flex-1 min-w-0">
        <!-- Category Badge -->
        <div class="flex items-center gap-2 mb-2">
          <span 
            class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
            :class="categoryClasses"
          >
            {{ paper.categoryLabel }}
          </span>
          <span v-if="paper.isOpenAccess" class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-success/10 text-success">
            دسترسی آزاد
          </span>
        </div>

        <!-- Title -->
        <h3 class="text-lg font-semibold text-slate-900 leading-tight mb-2 text-right group-hover:text-primary-600 transition-colors">
          <a 
            :href="`#/paper/${paper.id}`"
            class="focus-ring rounded-lg"
            @click.prevent="$emit('view', paper.id)"
          >
            {{ paper.title }}
          </a>
        </h3>

        <!-- Authors -->
        <div class="flex items-center gap-1 text-sm text-slate-600 mb-2 text-right">
          <UserIcon class="w-4 h-4 flex-shrink-0" />
          <span>نویسنده:</span>
          <div class="flex flex-wrap gap-1">
            <button
              v-for="(author, index) in paper.authors"
              :key="author"
              @click="$emit('authorClick', author)"
              class="hover:text-primary-600 focus-ring rounded px-1 transition-colors"
            >
              {{ author }}<span v-if="index < paper.authors.length - 1">،</span>
            </button>
          </div>
        </div>

        <!-- Journal and Date -->
        <div class="flex items-center gap-4 text-sm text-slate-500 mb-3">
          <div class="flex items-center gap-1">
            <BookOpenIcon class="w-4 h-4" />
            <span>{{ paper.journal }}</span>
          </div>
          <div class="flex items-center gap-1">
            <CalendarIcon class="w-4 h-4" />
            <span class="persian-date">{{ paper.date }}</span>
          </div>
        </div>
      </div>

      <!-- Actions Menu -->
      <div class="flex-shrink-0">
        <Menu as="div" class="relative">
          <Menu.Button class="p-2 text-slate-400 hover:text-slate-600 focus-ring rounded-lg">
            <EllipsisVerticalIcon class="w-5 h-5" />
          </Menu.Button>
          <Menu.Items class="absolute left-0 mt-2 w-48 neo-surface rounded-xl shadow-lg py-2 z-10">
            <Menu.Item v-slot="{ active }">
              <button
                @click="$emit('view', paper.id)"
                class="w-full text-right px-4 py-2 text-sm text-slate-700 hover:bg-slate-50 focus:bg-slate-50"
              >
                مشاهده جزئیات
              </button>
            </Menu.Item>
            <Menu.Item v-slot="{ active }">
              <button
                @click="toggleSave"
                class="w-full text-right px-4 py-2 text-sm text-slate-700 hover:bg-slate-50 focus:bg-slate-50"
              >
                {{ paper.saved ? 'حذف از ذخیره‌ها' : 'ذخیره مقاله' }}
              </button>
            </Menu.Item>
            <Menu.Item v-slot="{ active }">
              <button
                @click="$emit('cite', paper.id)"
                class="w-full text-right px-4 py-2 text-sm text-slate-700 hover:bg-slate-50 focus:bg-slate-50"
              >
                دریافت استناد
              </button>
            </Menu.Item>
            <Menu.Item v-slot="{ active }">
              <button
                @click="$emit('share', paper)"
                class="w-full text-right px-4 py-2 text-sm text-slate-700 hover:bg-slate-50 focus:bg-slate-50"
              >
                اشتراک‌گذاری
              </button>
            </Menu.Item>
          </Menu.Items>
        </Menu>
      </div>
    </div>

    <!-- Abstract Preview -->
    <div class="mb-4">
      <p class="text-sm text-slate-600 leading-relaxed text-right line-clamp-3">
        {{ paper.abstract }}
      </p>
    </div>

    <!-- Metrics -->
    <div class="flex items-center justify-between mb-4">
      <div class="flex items-center gap-4 text-sm text-slate-500">
        <!-- Citations -->
        <div class="flex items-center gap-1">
          <DocumentDuplicateIcon class="w-4 h-4" />
          <span>{{ formatPersianNumber(paper.citations) }} استناد</span>
        </div>
        
        <!-- DOI -->
        <div class="flex items-center gap-1">
          <LinkIcon class="w-4 h-4" />
          <span class="font-mono text-xs">{{ paper.doi }}</span>
        </div>
      </div>

      <!-- Social Actions -->
      <div class="flex items-center gap-2">
        <!-- Like Button -->
        <button
          @click="toggleLike"
          class="flex items-center gap-1 px-3 py-1 rounded-full text-sm transition-all duration-200 focus-ring"
          :class="isLiked ? 'bg-red-50 text-red-600' : 'bg-slate-100 text-slate-600 hover:bg-slate-200'"
        >
          <HeartIcon 
            class="w-4 h-4" 
            :class="{ 'fill-current': isLiked }"
          />
          <span>{{ formatPersianNumber(paper.likes) }}</span>
        </button>

        <!-- Comments -->
        <button
          @click="$emit('comments', paper.id)"
          class="flex items-center gap-1 px-3 py-1 rounded-full text-sm bg-slate-100 text-slate-600 hover:bg-slate-200 transition-colors focus-ring"
        >
          <ChatBubbleLeftIcon class="w-4 h-4" />
          <span>{{ formatPersianNumber(paper.comments) }}</span>
        </button>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex items-center gap-3">
      <!-- Read Abstract -->
      <button
        @click="$emit('view', paper.id)"
        class="flex-1 btn-secondary text-sm py-2"
      >
        خواندن چکیده
      </button>

      <!-- Download PDF -->
      <button
        v-if="paper.hasFullText"
        @click="$emit('download', paper.id)"
        class="flex-1 btn-primary text-sm py-2 flex items-center justify-center gap-2"
      >
        <ArrowDownTrayIcon class="w-4 h-4" />
        <span>دانلود PDF</span>
      </button>
      
      <!-- Request Access -->
      <button
        v-else
        @click="$emit('requestAccess', paper.id)"
        class="flex-1 btn-secondary text-sm py-2"
      >
        درخواست دسترسی
      </button>

      <!-- Save Button -->
      <button
        @click="toggleSave"
        class="p-2 rounded-lg transition-colors focus-ring"
        :class="paper.saved ? 'text-primary-600 bg-primary-50' : 'text-slate-400 hover:text-slate-600'"
        :aria-label="paper.saved ? 'حذف از ذخیره‌ها' : 'ذخیره مقاله'"
      >
        <BookmarkIcon 
          class="w-5 h-5" 
          :class="{ 'fill-current': paper.saved }"
        />
      </button>
    </div>

    <!-- Loading Overlay -->
    <div 
      v-if="isLoading"
      class="absolute inset-0 bg-white/80 backdrop-blur-sm rounded-xl flex items-center justify-center"
    >
      <div class="loading-spinner"></div>
    </div>
  </article>
</template>

<script setup>
import { ref, computed } from 'vue'
import { Menu } from '@headlessui/vue'
import {
  UserIcon,
  BookOpenIcon,
  CalendarIcon,
  EllipsisVerticalIcon,
  DocumentDuplicateIcon,
  LinkIcon,
  HeartIcon,
  ChatBubbleLeftIcon,
  ArrowDownTrayIcon,
  BookmarkIcon
} from '@heroicons/vue/24/outline'
import { formatPersianNumber } from '@/utils/persian'

// Props
const props = defineProps({
  paper: {
    type: Object,
    required: true
  },
  isSelected: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits([
  'view', 'download', 'requestAccess', 'save', 'like', 
  'comments', 'cite', 'share', 'authorClick'
])

// Reactive state
const isLoading = ref(false)
const isLiked = ref(false)

// Computed properties
const categoryClasses = computed(() => {
  const baseClasses = 'bg-opacity-10'
  const categoryColors = {
    'medicine': 'bg-red-500 text-red-700',
    'technology': 'bg-blue-500 text-blue-700',
    'social': 'bg-green-500 text-green-700',
    'engineering': 'bg-yellow-500 text-yellow-700',
    'education': 'bg-purple-500 text-purple-700'
  }
  
  return `${categoryColors[props.paper.category] || 'bg-slate-500 text-slate-700'} ${baseClasses}`
})

// Methods
const toggleSave = async () => {
  isLoading.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 500)) // Simulate API call
    emit('save', props.paper.id)
  } finally {
    isLoading.value = false
  }
}

const toggleLike = async () => {
  isLiked.value = !isLiked.value
  emit('like', props.paper.id)
}
</script>

<style scoped>
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
