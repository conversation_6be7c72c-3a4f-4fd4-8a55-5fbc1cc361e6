<template>
  <nav 
    dir="rtl" 
    class="mobile-nav md:hidden"
    aria-label="ناوبری پایین"
  >
    <div class="max-w-md mx-auto px-4 py-2">
      <div class="grid grid-cols-3 gap-1">
        
        <!-- Home -->
        <button 
          @click="navigateTo('home')"
          class="flex flex-col items-center justify-center py-2 px-3 rounded-xl transition-all duration-200"
          :class="activeTab === 'home' ? 'bg-primary-500 text-white shadow-lg' : 'text-slate-600 hover:text-slate-900 hover:bg-slate-100'"
          :aria-current="activeTab === 'home' ? 'page' : undefined"
        >
          <HomeIcon class="w-6 h-6 mb-1" />
          <span class="text-xs font-medium">خانه</span>
        </button>

        <!-- Advanced Search -->
        <button 
          @click="navigateTo('search')"
          class="flex flex-col items-center justify-center py-2 px-3 rounded-xl transition-all duration-200"
          :class="activeTab === 'search' ? 'bg-primary-500 text-white shadow-lg' : 'text-slate-600 hover:text-slate-900 hover:bg-slate-100'"
          :aria-current="activeTab === 'search' ? 'page' : undefined"
        >
          <MagnifyingGlassIcon class="w-6 h-6 mb-1" />
          <span class="text-xs font-medium">جستجوی پیشرفته</span>
        </button>

        <!-- Profile -->
        <button 
          @click="navigateTo('profile')"
          class="flex flex-col items-center justify-center py-2 px-3 rounded-xl transition-all duration-200 relative"
          :class="activeTab === 'profile' ? 'bg-primary-500 text-white shadow-lg' : 'text-slate-600 hover:text-slate-900 hover:bg-slate-100'"
          :aria-current="activeTab === 'profile' ? 'page' : undefined"
        >
          <UserIcon class="w-6 h-6 mb-1" />
          <span class="text-xs font-medium">پروفایل</span>
          
          <!-- Notification Badge (if user is logged in) -->
          <div 
            v-if="hasNotifications" 
            class="notification-badge"
            aria-label="اعلان جدید"
          >
            {{ notificationCount }}
          </div>
        </button>
      </div>
    </div>

    <!-- Quick Action Buttons (Floating) -->
    <div class="absolute -top-6 left-1/2 transform -translate-x-1/2">
      <button
        @click="openQuickSearch"
        class="w-12 h-12 bg-primary-500 text-white rounded-full shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-200 focus-ring"
        aria-label="جستجوی سریع"
      >
        <MagnifyingGlassIcon class="w-6 h-6 mx-auto" />
      </button>
    </div>
  </nav>
</template>

<script setup>
import { ref, computed } from 'vue'
import { 
  HomeIcon, 
  MagnifyingGlassIcon, 
  UserIcon 
} from '@heroicons/vue/24/outline'

// Props
const props = defineProps({
  currentTab: {
    type: String,
    default: 'home'
  },
  notifications: {
    type: Number,
    default: 0
  }
})

// Emits
const emit = defineEmits(['navigate', 'quickSearch'])

// Reactive state
const activeTab = ref(props.currentTab)

// Computed properties
const hasNotifications = computed(() => props.notifications > 0)
const notificationCount = computed(() => {
  if (props.notifications > 99) return '99+'
  return props.notifications.toString()
})

// Methods
const navigateTo = (tab) => {
  activeTab.value = tab
  emit('navigate', tab)
  
  // Add haptic feedback on mobile devices
  if ('vibrate' in navigator) {
    navigator.vibrate(50)
  }
}

const openQuickSearch = () => {
  emit('quickSearch')
  
  // Add haptic feedback
  if ('vibrate' in navigator) {
    navigator.vibrate(100)
  }
}

// Watch for prop changes
import { watch } from 'vue'
watch(() => props.currentTab, (newTab) => {
  activeTab.value = newTab
})
</script>

<style scoped>
/* Additional mobile-specific styles */
.mobile-nav {
  /* Ensure proper safe area handling */
  padding-bottom: max(env(safe-area-inset-bottom), 0.5rem);
}

/* Smooth transitions for tab switching */
.mobile-nav button {
  transform-origin: center;
}

.mobile-nav button:active {
  transform: scale(0.95);
}

/* Enhanced focus styles for mobile */
@media (max-width: 768px) {
  .focus-ring:focus {
    outline: 2px solid #125CC8;
    outline-offset: 2px;
  }
}
</style>
