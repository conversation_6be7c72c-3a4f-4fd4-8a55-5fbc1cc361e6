<!DOCTYPE html>
<html lang="fa" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ScholarHub - پایگاه داده مقالات علمی</title>
    
    <!-- Persian Font - Vazirmatn -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Vazirmatn:wght@100;200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Meta tags for RTL and Persian -->
    <meta name="description" content="پایگاه داده جامع مقالات علمی و پژوهشی - جستجو در میلیون‌ها مقاله علمی">
    <meta name="keywords" content="مقالات علمی, پژوهش, دانشگاه, علم, فناوری, پزشکی">
    <meta property="og:title" content="ScholarHub - پایگاه داده مقالات علمی">
    <meta property="og:description" content="پایگاه داده جامع مقالات علمی و پژوهشی">
    <meta property="og:type" content="website">
    
    <style>
      /* Ensure RTL direction is applied immediately */
      html[dir="rtl"] {
        direction: rtl;
      }
      
      /* Loading animation */
      .loading-spinner {
        border: 3px solid #f3f3f3;
        border-top: 3px solid #125CC8;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        animation: spin 1s linear infinite;
        margin: 20px auto;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
  </head>
  <body class="font-vazir bg-slate-50 text-slate-900">
    <div id="app">
      <div class="loading-spinner"></div>
      <p class="text-center text-slate-600 mt-4">در حال بارگذاری...</p>
    </div>
    <script type="module" src="/src/main.js"></script>
  </body>
</html>
