<template>
  <div class="space-y-6" dir="rtl">
    
    <!-- Platform Overview Stats -->
    <div class="neo-card p-6">
      <h2 class="text-persian-h3 text-slate-900 mb-6 text-right">آمار کلی پلتفرم</h2>
      
      <div class="grid grid-cols-2 lg:grid-cols-4 gap-4">
        <div 
          v-for="stat in overviewStats" 
          :key="stat.key"
          class="text-center p-4 neo-surface rounded-xl hover:shadow-neo-lg transition-all duration-300"
        >
          <div class="w-12 h-12 mx-auto mb-3 rounded-xl bg-gradient-primary flex items-center justify-center">
            <component :is="stat.icon" class="w-6 h-6 text-white" />
          </div>
          <div class="text-2xl font-bold text-slate-900 mb-1">
            {{ formatPersianNumber(stat.value) }}+
          </div>
          <div class="text-sm text-slate-600">{{ stat.label }}</div>
        </div>
      </div>
    </div>

    <!-- Articles by Category Chart -->
    <div class="neo-card p-6">
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-persian-h3 text-slate-900 text-right">توزیع مقالات بر اساس دسته‌بندی</h3>
        <button
          @click="toggleChartView"
          class="text-sm text-primary-600 hover:text-primary-700 focus-ring rounded-lg px-3 py-1"
        >
          {{ showTable ? 'نمایش نمودار' : 'نمایش جدول' }}
        </button>
      </div>

      <!-- Chart View -->
      <div v-if="!showTable" class="relative">
        <div class="h-64 flex items-center justify-center">
          <!-- Simple Bar Chart using CSS -->
          <div class="w-full max-w-md">
            <div class="space-y-3">
              <div 
                v-for="category in categoryStats" 
                :key="category.name"
                class="flex items-center gap-3"
              >
                <div class="w-24 text-sm text-slate-700 text-right flex-shrink-0">
                  {{ category.name }}
                </div>
                <div class="flex-1 bg-slate-200 rounded-full h-6 relative overflow-hidden">
                  <div 
                    class="h-full rounded-full transition-all duration-1000 ease-out flex items-center justify-end px-2"
                    :style="{ 
                      width: `${(category.value / maxCategoryValue) * 100}%`,
                      backgroundColor: category.color 
                    }"
                  >
                    <span class="text-xs text-white font-medium">
                      {{ formatPersianNumber(category.value) }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Table View -->
      <div v-else class="overflow-x-auto">
        <table class="w-full text-right">
          <thead>
            <tr class="border-b border-slate-200">
              <th class="py-3 px-4 text-sm font-semibold text-slate-900">دسته‌بندی</th>
              <th class="py-3 px-4 text-sm font-semibold text-slate-900">تعداد مقالات</th>
              <th class="py-3 px-4 text-sm font-semibold text-slate-900">درصد</th>
            </tr>
          </thead>
          <tbody>
            <tr 
              v-for="category in categoryStats" 
              :key="category.name"
              class="border-b border-slate-100 hover:bg-slate-50"
            >
              <td class="py-3 px-4 text-sm text-slate-900">
                <div class="flex items-center gap-2">
                  <div 
                    class="w-3 h-3 rounded-full"
                    :style="{ backgroundColor: category.color }"
                  ></div>
                  {{ category.name }}
                </div>
              </td>
              <td class="py-3 px-4 text-sm text-slate-700">
                {{ formatPersianNumber(category.value) }}
              </td>
              <td class="py-3 px-4 text-sm text-slate-700">
                {{ formatPersianNumber(Math.round((category.value / totalArticles) * 100)) }}%
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Growth Chart -->
    <div class="neo-card p-6">
      <h3 class="text-persian-h3 text-slate-900 mb-6 text-right">روند رشد مقالات (سالانه)</h3>
      
      <div class="h-48 flex items-end justify-center gap-4 px-4">
        <div 
          v-for="(data, index) in growthData" 
          :key="data.year"
          class="flex flex-col items-center gap-2 flex-1 max-w-16"
        >
          <!-- Bar -->
          <div class="w-full bg-slate-200 rounded-t-lg relative overflow-hidden" style="height: 120px;">
            <div 
              class="w-full bg-gradient-primary rounded-t-lg transition-all duration-1000 ease-out absolute bottom-0"
              :style="{ 
                height: `${(data.papers / maxGrowthValue) * 100}%`,
                animationDelay: `${index * 200}ms`
              }"
            ></div>
          </div>
          
          <!-- Value -->
          <div class="text-xs font-medium text-slate-700">
            {{ formatPersianNumber(data.papers) }}
          </div>
          
          <!-- Year -->
          <div class="text-xs text-slate-500">
            {{ data.year }}
          </div>
        </div>
      </div>

      <!-- Growth Summary -->
      <div class="mt-6 p-4 bg-slate-50 rounded-xl">
        <div class="flex items-center justify-between text-sm">
          <div class="text-slate-600">رشد سالانه میانگین:</div>
          <div class="font-semibold text-success">{{ formatPersianNumber(averageGrowth) }}%</div>
        </div>
      </div>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- Recent Activity -->
      <div class="neo-card p-6">
        <h3 class="text-lg font-semibold text-slate-900 mb-4 text-right">فعالیت اخیر</h3>
        <div class="space-y-3">
          <div class="flex items-center justify-between text-sm">
            <span class="text-slate-600">مقالات امروز:</span>
            <span class="font-semibold text-slate-900">{{ formatPersianNumber(245) }}</span>
          </div>
          <div class="flex items-center justify-between text-sm">
            <span class="text-slate-600">کاربران آنلاین:</span>
            <span class="font-semibold text-success">{{ formatPersianNumber(1847) }}</span>
          </div>
          <div class="flex items-center justify-between text-sm">
            <span class="text-slate-600">دانلودهای امروز:</span>
            <span class="font-semibold text-slate-900">{{ formatPersianNumber(5632) }}</span>
          </div>
        </div>
      </div>

      <!-- Top Categories -->
      <div class="neo-card p-6">
        <h3 class="text-lg font-semibold text-slate-900 mb-4 text-right">محبوب‌ترین دسته‌ها</h3>
        <div class="space-y-3">
          <div 
            v-for="(category, index) in categoryStats.slice(0, 3)" 
            :key="category.name"
            class="flex items-center gap-3"
          >
            <div class="w-6 h-6 rounded-full bg-slate-200 flex items-center justify-center text-xs font-bold text-slate-600">
              {{ formatPersianNumber(index + 1) }}
            </div>
            <div class="flex-1 text-sm text-slate-700">{{ category.name }}</div>
            <div class="text-sm font-semibold text-slate-900">
              {{ formatPersianNumber(category.value) }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import {
  DocumentTextIcon,
  UserIcon,
  BuildingLibraryIcon,
  ArrowDownTrayIcon
} from '@heroicons/vue/24/outline'
import { formatPersianNumber } from '@/utils/persian'
import { platformStats, categoryStats, growthData } from '@/data/mockData'

// Reactive state
const showTable = ref(false)

// Overview stats configuration
const overviewStats = [
  {
    key: 'papers',
    label: 'مقاله',
    value: platformStats.totalPapers,
    icon: DocumentTextIcon
  },
  {
    key: 'researchers',
    label: 'پژوهشگر',
    value: platformStats.totalResearchers,
    icon: UserIcon
  },
  {
    key: 'universities',
    label: 'دانشگاه',
    value: platformStats.totalUniversities,
    icon: BuildingLibraryIcon
  },
  {
    key: 'downloads',
    label: 'دانلود',
    value: platformStats.totalDownloads,
    icon: ArrowDownTrayIcon
  }
]

// Computed properties
const maxCategoryValue = computed(() => {
  return Math.max(...categoryStats.map(cat => cat.value))
})

const maxGrowthValue = computed(() => {
  return Math.max(...growthData.map(data => data.papers))
})

const totalArticles = computed(() => {
  return categoryStats.reduce((sum, cat) => sum + cat.value, 0)
})

const averageGrowth = computed(() => {
  if (growthData.length < 2) return 0
  
  const firstYear = growthData[0].papers
  const lastYear = growthData[growthData.length - 1].papers
  const years = growthData.length - 1
  
  return Math.round(((lastYear / firstYear) ** (1 / years) - 1) * 100)
})

// Methods
const toggleChartView = () => {
  showTable.value = !showTable.value
}
</script>

<style scoped>
/* Animation for chart bars */
@keyframes growBar {
  from {
    height: 0%;
  }
  to {
    height: var(--final-height);
  }
}

.chart-bar {
  animation: growBar 1s ease-out forwards;
}
</style>
