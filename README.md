# ScholarHub - پایگاه داده مقالات علمی

A responsive Vue 3 homepage for an RTL Persian scientific-paper database with modern neomorphism design and academic aesthetic.

## ✨ Features

- **RTL Persian Support**: Full right-to-left layout with Persian language support
- **Modern Neomorphism Design**: Clean, academic aesthetic with subtle shadows and glass effects
- **Responsive Design**: Works perfectly on Desktop, Tablet, and Mobile devices
- **Accessibility**: WCAG compliant with screen reader support and keyboard navigation
- **Interactive Components**: 
  - Hero search with Persian autocomplete
  - Trending papers grid with detailed cards
  - News and announcements
  - Interactive statistics charts
  - Mobile bottom navigation

## 🛠 Tech Stack

- **Vue 3** with Composition API
- **TailwindCSS** with RTL plugin
- **HeadlessUI** for accessible components
- **Heroicons** for consistent iconography
- **Chart.js** for data visualization
- **Vite** for fast development

## 📦 Installation

1. Install dependencies:
```bash
npm install
```

2. Start development server:
```bash
npm run dev
```

3. Build for production:
```bash
npm run build
```

## 🎨 Design System

### Colors
- **Primary**: #125CC8 (Academic Blue)
- **Accent**: #0ea5e9 (Sky Blue)
- **Success**: #10b981 (Emerald)
- **Surface**: #f8fafc (Slate 50)

### Typography
- **Persian Font**: Vazirmatn (Google Fonts)
- **Serif**: Source Serif Pro for articles
- **Weights**: 400 (body), 600 (headings), 700 (CTAs)

### Spacing
- **8px baseline grid**
- **Consistent padding and margins**
- **Responsive breakpoints**: sm/md/lg/xl/2xl

## 🧩 Components

### Core Layout
- `AppLayout.vue` - Main layout wrapper with RTL support
- `TopNavigation.vue` - Header with language switcher and accessibility menu
- `MobileBottomNav.vue` - Mobile navigation with Persian labels

### Content Components
- `SearchHero.vue` - Hero section with Persian autocomplete
- `PaperCard.vue` - Paper display cards with Persian metadata
- `NewsItem.vue` - News and announcements
- `StatsPanel.vue` - Interactive charts and statistics

### Utility Components
- `QuickSearchModal.vue` - Modal search interface

## 🌐 RTL & Persian Support

- **Direction**: `dir="rtl"` on all containers
- **Language**: `lang="fa"` for proper screen reader support
- **Numerals**: Persian numerals (۰۱۲۳...) with conversion utilities
- **Date Formatting**: Persian date format (YYYY/MM/DD)
- **Text Alignment**: Right-aligned text throughout

## ♿ Accessibility Features

- **WCAG 2.1 AA Compliant**
- **Keyboard Navigation**: Full keyboard support
- **Screen Reader**: Proper ARIA labels in Persian
- **Focus Management**: Visible focus indicators
- **High Contrast**: Support for high contrast mode
- **Reduced Motion**: Respects user motion preferences

## 📱 Mobile Features

- **Bottom Navigation**: Persian labels (خانه، جستجوی پیشرفته، پروفایل)
- **Touch Interactions**: Optimized for mobile devices
- **Safe Area**: Proper handling of device safe areas
- **Responsive Grid**: Adapts to different screen sizes

## 🔧 Configuration

### Tailwind Config
The project includes a custom Tailwind configuration with:
- RTL plugin for proper right-to-left support
- Custom color palette for academic theme
- Neomorphism shadow utilities
- Persian font family setup

### Persian Utilities
Located in `src/utils/persian.js`:
- Number formatting with Persian numerals
- Date formatting functions
- Text truncation for RTL
- Academic field translations

## 📊 Mock Data

Comprehensive Persian mock data included:
- **Search Suggestions**: Persian paper titles and authors
- **Trending Papers**: Academic papers with Persian metadata
- **News Items**: Persian announcements and updates
- **Statistics**: Platform stats and category data

## 🎯 Usage Examples

### Basic Search
```javascript
// Handle search with Persian query
const handleSearch = (searchData) => {
  console.log('Search query:', searchData.query)
  console.log('Active filters:', searchData.filters)
}
```

### Paper Actions
```javascript
// Handle paper interactions
const handlePaperAction = (action, paperId) => {
  switch(action) {
    case 'view': // View paper details
    case 'download': // Download PDF
    case 'save': // Save to library
    case 'cite': // Get citation
  }
}
```

## 🚀 Deployment

Build the project for production:
```bash
npm run build
```

The built files will be in the `dist/` directory, ready for deployment to any static hosting service.

## 📄 License

This project is created for educational and demonstration purposes.

---

**Note**: This is a frontend-only implementation. For a complete application, you'll need to integrate with a backend API for data fetching and user management.
