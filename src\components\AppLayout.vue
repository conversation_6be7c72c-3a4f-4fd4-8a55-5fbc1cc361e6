<template>
  <div 
    dir="rtl" 
    lang="fa" 
    class="min-h-screen bg-slate-50"
    :class="{ 'high-contrast': highContrast, 'reduced-motion': reducedMotion }"
  >
    <!-- Skip to main content link for screen readers -->
    <a 
      href="#main-content" 
      class="sr-only-focusable"
      @click="skipToMain"
    >
      پرش به محتوای اصلی
    </a>

    <!-- Top Navigation -->
    <TopNavigation />

    <!-- Main Content Area -->
    <main 
      id="main-content"
      class="relative"
      :class="{ 'pb-20': showMobileNav }"
      tabindex="-1"
    >
      <slot />
    </main>

    <!-- Mobile Bottom Navigation -->
    <MobileBottomNav 
      v-if="showMobileNav"
      :current-tab="currentMobileTab"
      :notifications="notificationCount"
      @navigate="handleMobileNavigation"
      @quick-search="handleQuickSearch"
    />

    <!-- Quick Search Modal -->
    <QuickSearchModal 
      v-if="showQuickSearch"
      @close="showQuickSearch = false"
      @search="handleQuickSearchSubmit"
    />

    <!-- Loading Overlay -->
    <div 
      v-if="isLoading"
      class="fixed inset-0 bg-black/20 backdrop-blur-sm z-50 flex items-center justify-center"
      aria-live="polite"
      aria-label="در حال بارگذاری"
    >
      <div class="neo-surface rounded-xl p-6 flex flex-col items-center gap-4">
        <div class="loading-spinner"></div>
        <p class="text-slate-700 font-medium">{{ loadingMessage }}</p>
      </div>
    </div>

    <!-- Toast Notifications -->
    <div 
      class="fixed top-20 left-4 right-4 z-40 space-y-2"
      aria-live="polite"
    >
      <div
        v-for="toast in toasts"
        :key="toast.id"
        class="neo-surface rounded-xl p-4 flex items-center gap-3 animate-slide-up"
        :class="{
          'border-r-4 border-success': toast.type === 'success',
          'border-r-4 border-danger': toast.type === 'error',
          'border-r-4 border-warning': toast.type === 'warning',
          'border-r-4 border-primary-500': toast.type === 'info'
        }"
      >
        <div class="flex-1 text-right">
          <p class="font-medium text-slate-900">{{ toast.title }}</p>
          <p v-if="toast.message" class="text-sm text-slate-600 mt-1">{{ toast.message }}</p>
        </div>
        <button
          @click="removeToast(toast.id)"
          class="text-slate-400 hover:text-slate-600 focus-ring rounded-lg p-1"
          aria-label="بستن اعلان"
        >
          <XMarkIcon class="w-5 h-5" />
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { XMarkIcon } from '@heroicons/vue/24/outline'
import TopNavigation from './TopNavigation.vue'
import MobileBottomNav from './MobileBottomNav.vue'
import QuickSearchModal from './QuickSearchModal.vue'

// Props
const props = defineProps({
  loading: {
    type: Boolean,
    default: false
  },
  loadingMessage: {
    type: String,
    default: 'در حال بارگذاری...'
  }
})

// Emits
const emit = defineEmits(['navigate', 'search'])

// Reactive state
const showMobileNav = ref(true)
const currentMobileTab = ref('home')
const showQuickSearch = ref(false)
const notificationCount = ref(0)
const isLoading = ref(props.loading)
const toasts = ref([])

// Accessibility settings
const highContrast = ref(false)
const reducedMotion = ref(false)

// Screen size detection
const isMobile = ref(false)

// Methods
const handleMobileNavigation = (tab) => {
  currentMobileTab.value = tab
  emit('navigate', tab)
}

const handleQuickSearch = () => {
  showQuickSearch.value = true
}

const handleQuickSearchSubmit = (query) => {
  showQuickSearch.value = false
  emit('search', query)
}

const skipToMain = () => {
  const mainContent = document.getElementById('main-content')
  if (mainContent) {
    mainContent.focus()
  }
}

// Toast management
let toastId = 0
const addToast = (toast) => {
  const id = ++toastId
  toasts.value.push({ ...toast, id })
  
  // Auto remove after 5 seconds
  setTimeout(() => {
    removeToast(id)
  }, 5000)
  
  return id
}

const removeToast = (id) => {
  const index = toasts.value.findIndex(toast => toast.id === id)
  if (index > -1) {
    toasts.value.splice(index, 1)
  }
}

// Screen size detection
const updateScreenSize = () => {
  isMobile.value = window.innerWidth < 768
  showMobileNav.value = isMobile.value
}

// Accessibility preferences detection
const detectAccessibilityPreferences = () => {
  // Check for reduced motion preference
  if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
    reducedMotion.value = true
  }
  
  // Check for high contrast preference
  if (window.matchMedia('(prefers-contrast: high)').matches) {
    highContrast.value = true
  }
}

// Keyboard shortcuts
const handleKeyboardShortcuts = (event) => {
  // Ctrl/Cmd + K for quick search
  if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
    event.preventDefault()
    showQuickSearch.value = true
  }
  
  // Escape to close modals
  if (event.key === 'Escape') {
    showQuickSearch.value = false
  }
}

// Lifecycle
onMounted(() => {
  updateScreenSize()
  detectAccessibilityPreferences()
  
  window.addEventListener('resize', updateScreenSize)
  window.addEventListener('keydown', handleKeyboardShortcuts)
  
  // Listen for accessibility preference changes
  window.matchMedia('(prefers-reduced-motion: reduce)').addEventListener('change', (e) => {
    reducedMotion.value = e.matches
  })
  
  window.matchMedia('(prefers-contrast: high)').addEventListener('change', (e) => {
    highContrast.value = e.matches
  })
})

onUnmounted(() => {
  window.removeEventListener('resize', updateScreenSize)
  window.removeEventListener('keydown', handleKeyboardShortcuts)
})

// Expose methods for parent components
defineExpose({
  addToast,
  removeToast
})
</script>

<style scoped>
/* High contrast mode adjustments */
.high-contrast {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.high-contrast .neo-surface {
  border: 2px solid #000;
  background-color: #fff;
}

/* Reduced motion adjustments */
.reduced-motion * {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
}

/* Loading spinner */
.loading-spinner {
  border: 3px solid #f3f3f3;
  border-top: 3px solid #125CC8;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Safe area handling for mobile */
@supports (padding-bottom: env(safe-area-inset-bottom)) {
  .pb-20 {
    padding-bottom: calc(5rem + env(safe-area-inset-bottom));
  }
}
</style>
