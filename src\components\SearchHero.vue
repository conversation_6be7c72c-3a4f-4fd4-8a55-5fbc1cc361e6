<template>
  <section 
    dir="rtl" 
    lang="fa" 
    class="py-12 lg:py-20 relative overflow-hidden"
  >
    <!-- Background Animation -->
    <div class="absolute inset-0 opacity-5">
      <div class="absolute top-10 right-10 w-32 h-32 bg-primary-500 rounded-full animate-pulse"></div>
      <div class="absolute bottom-20 left-20 w-24 h-24 bg-accent-500 rounded-full animate-pulse" style="animation-delay: 1s;"></div>
      <div class="absolute top-1/2 left-1/3 w-16 h-16 bg-success rounded-full animate-pulse" style="animation-delay: 2s;"></div>
    </div>

    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 relative">
      
      <!-- Hero Content -->
      <div class="text-center mb-12">
        <h1 class="text-persian-h1 text-slate-900 mb-4 animate-fade-in">
          در میلیون‌ها مقاله پژوهشی جستجو کنید
        </h1>
        <p class="text-persian-body text-slate-600 max-w-2xl mx-auto animate-fade-in" style="animation-delay: 0.2s;">
          مقالات، نویسندگان و داده‌های تحقیقاتی را در بزرگ‌ترین پایگاه داده علمی فارسی‌زبان بیابید
        </p>
        
        <!-- Platform Stats -->
        <div class="flex items-center justify-center gap-8 mt-8 animate-fade-in" style="animation-delay: 0.4s;">
          <div class="text-center">
            <div class="text-2xl font-bold text-primary-600">{{ formatPersianNumber(platformStats.totalPapers) }}+</div>
            <div class="text-sm text-slate-600">مقاله</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-primary-600">{{ formatPersianNumber(platformStats.totalResearchers) }}+</div>
            <div class="text-sm text-slate-600">پژوهشگر</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-primary-600">{{ formatPersianNumber(platformStats.totalUniversities) }}+</div>
            <div class="text-sm text-slate-600">دانشگاه</div>
          </div>
        </div>
      </div>

      <!-- Search Card -->
      <div class="max-w-4xl mx-auto">
        <div 
          class="neo-surface rounded-2xl p-6 lg:p-8 animate-fade-in hover:shadow-neo-lg transition-all duration-500"
          style="animation-delay: 0.6s;"
        >
          
          <!-- Main Search -->
          <div class="relative">
            <Combobox v-model="selectedSuggestion" as="div">
              <div class="relative">
                
                <!-- Search Input Container -->
                <div 
                  class="flex items-center gap-3 bg-white/40 backdrop-blur-xs rounded-xl p-2 border-2 border-transparent transition-all duration-300"
                  :class="{ 
                    'border-primary-300 shadow-lg animate-pulse-glow': isFocused,
                    'hover:border-slate-200': !isFocused 
                  }"
                >
                  <div class="flex-1 relative">
                    <Combobox.Input
                      ref="searchInput"
                      v-model="query"
                      class="search-input"
                      placeholder="جستجو در میلیون‌ها مقاله: عنوان، نویسنده، DOI یا واژه کلیدی..."
                      @focus="handleFocus"
                      @blur="handleBlur"
                      @keydown.enter.prevent="submitSearch"
                      autocomplete="off"
                      spellcheck="false"
                      dir="rtl"
                    />
                    
                    <!-- Search Icon -->
                    <div class="absolute left-4 top-1/2 transform -translate-y-1/2 text-slate-400">
                      <MagnifyingGlassIcon class="w-6 h-6" />
                    </div>
                  </div>
                  
                  <!-- Search Button -->
                  <button
                    @click="submitSearch"
                    class="btn-primary px-6 py-3 flex items-center gap-2 whitespace-nowrap"
                    :disabled="!query.trim()"
                  >
                    <span>جستجو</span>
                    <MagnifyingGlassIcon class="w-5 h-5" />
                  </button>
                </div>

                <!-- Autocomplete Suggestions -->
                <Combobox.Options 
                  v-if="filteredSuggestions.length > 0"
                  class="absolute z-50 mt-2 w-full neo-surface rounded-xl shadow-lg max-h-80 overflow-auto py-2"
                >
                  <Combobox.Option
                    v-for="suggestion in filteredSuggestions"
                    :key="suggestion.id"
                    :value="suggestion"
                    class="cursor-pointer px-4 py-3 hover:bg-slate-50 focus:bg-slate-50 transition-colors"
                    v-slot="{ active, selected }"
                  >
                    <div class="flex items-center gap-3 text-right">
                      <!-- Type Icon -->
                      <div class="flex-shrink-0">
                        <DocumentTextIcon v-if="suggestion.type === 'paper'" class="w-5 h-5 text-primary-500" />
                        <UserIcon v-else-if="suggestion.type === 'author'" class="w-5 h-5 text-success" />
                        <BookOpenIcon v-else class="w-5 h-5 text-accent-500" />
                      </div>
                      
                      <!-- Content -->
                      <div class="flex-1 min-w-0">
                        <div class="font-medium text-slate-900 truncate">{{ suggestion.title }}</div>
                        <div class="text-sm text-slate-500 truncate">{{ suggestion.meta }}</div>
                      </div>
                      
                      <!-- Category Badge -->
                      <div class="flex-shrink-0">
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-slate-100 text-slate-700">
                          {{ getPersianFieldName(suggestion.category) }}
                        </span>
                      </div>
                    </div>
                  </Combobox.Option>
                </Combobox.Options>
              </div>
            </Combobox>
          </div>

          <!-- Filter Chips -->
          <div class="mt-6 flex flex-wrap gap-2 justify-center">
            <button
              v-for="category in filterCategories.slice(0, 8)"
              :key="category.id"
              @click="toggleFilter(category.id)"
              class="filter-chip"
              :class="{ 'active': activeFilters.includes(category.id) }"
            >
              {{ category.label }}
              <span class="mr-1 text-xs opacity-75">({{ formatPersianNumber(category.count) }})</span>
            </button>
          </div>

          <!-- Advanced Search Link -->
          <div class="mt-6 text-center">
            <button
              @click="openAdvancedSearch"
              class="text-primary-600 hover:text-primary-700 font-medium text-sm focus-ring rounded-lg px-3 py-1"
            >
              جستجوی پیشرفته
              <ChevronLeftIcon class="w-4 h-4 inline mr-1 transform rotate-180" />
            </button>
          </div>
        </div>
      </div>

      <!-- Quick Access Categories -->
      <div class="mt-12 max-w-4xl mx-auto animate-fade-in" style="animation-delay: 0.8s;">
        <h2 class="text-persian-h3 text-slate-900 text-center mb-6">دسته‌بندی‌های محبوب</h2>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
          <button
            v-for="category in popularCategories"
            :key="category.id"
            @click="searchByCategory(category.id)"
            class="neo-card p-4 text-center hover-lift group"
          >
            <div class="w-12 h-12 mx-auto mb-3 rounded-xl bg-gradient-primary flex items-center justify-center group-hover:scale-110 transition-transform">
              <component :is="category.icon" class="w-6 h-6 text-white" />
            </div>
            <div class="font-medium text-slate-900 text-sm">{{ category.label }}</div>
            <div class="text-xs text-slate-500 mt-1">{{ formatPersianNumber(category.count) }} مقاله</div>
          </button>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue'
import { Combobox } from '@headlessui/vue'
import { 
  MagnifyingGlassIcon,
  DocumentTextIcon,
  UserIcon,
  BookOpenIcon,
  ChevronLeftIcon,
  BeakerIcon,
  HeartIcon,
  CogIcon,
  AcademicCapIcon
} from '@heroicons/vue/24/outline'

import { formatPersianNumber, getPersianFieldName } from '@/utils/persian'
import { searchSuggestions, filterCategories, platformStats } from '@/data/mockData'

// Emits
const emit = defineEmits(['search', 'advancedSearch', 'categorySearch'])

// Reactive state
const query = ref('')
const selectedSuggestion = ref(null)
const isFocused = ref(false)
const activeFilters = ref([])
const searchInput = ref(null)

// Popular categories with icons
const popularCategories = [
  { id: 'medicine', label: 'پزشکی', count: 220000, icon: HeartIcon },
  { id: 'technology', label: 'علوم و فناوری', count: 285000, icon: CogIcon },
  { id: 'basic-sciences', label: 'علوم پایه', count: 145000, icon: BeakerIcon },
  { id: 'education', label: 'علوم تربیتی', count: 95000, icon: AcademicCapIcon }
]

// Computed properties
const filteredSuggestions = computed(() => {
  if (!query.value) return []
  
  return searchSuggestions.filter(suggestion =>
    suggestion.title.includes(query.value) ||
    suggestion.meta.includes(query.value)
  ).slice(0, 8)
})

// Methods
const handleFocus = () => {
  isFocused.value = true
}

const handleBlur = () => {
  // Delay to allow for suggestion selection
  setTimeout(() => {
    isFocused.value = false
  }, 200)
}

const submitSearch = () => {
  if (!query.value.trim()) return
  
  emit('search', {
    query: query.value,
    filters: activeFilters.value
  })
}

const toggleFilter = (filterId) => {
  const index = activeFilters.value.indexOf(filterId)
  if (index > -1) {
    activeFilters.value.splice(index, 1)
  } else {
    activeFilters.value.push(filterId)
  }
}

const openAdvancedSearch = () => {
  emit('advancedSearch')
}

const searchByCategory = (categoryId) => {
  emit('categorySearch', categoryId)
}

// Auto-focus search input on mount
onMounted(() => {
  nextTick(() => {
    if (searchInput.value) {
      searchInput.value.focus()
    }
  })
})

// Watch for suggestion selection
import { watch } from 'vue'
watch(selectedSuggestion, (newSuggestion) => {
  if (newSuggestion) {
    query.value = newSuggestion.title
    submitSearch()
  }
})
</script>
