// Persian/Farsi utility functions

/**
 * Convert Western numerals to Persian numerals
 * @param {string|number} input - The input to convert
 * @returns {string} - Persian numerals
 */
export function toPersianNumerals(input) {
  const persianDigits = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹']
  return String(input).replace(/\d/g, (digit) => persianDigits[parseInt(digit)])
}

/**
 * Convert Persian numerals to Western numerals
 * @param {string} input - The input to convert
 * @returns {string} - Western numerals
 */
export function toWesternNumerals(input) {
  const westernDigits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9']
  const persianDigits = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹']
  
  return String(input).replace(/[۰-۹]/g, (digit) => {
    const index = persianDigits.indexOf(digit)
    return westernDigits[index]
  })
}

/**
 * Format date in Persian format (YYYY/MM/DD with Persian numerals)
 * @param {Date|string} date - The date to format
 * @returns {string} - Formatted Persian date
 */
export function formatPersianDate(date) {
  const dateObj = new Date(date)
  const year = dateObj.getFullYear()
  const month = String(dateObj.getMonth() + 1).padStart(2, '0')
  const day = String(dateObj.getDate()).padStart(2, '0')
  
  return toPersianNumerals(`${year}/${month}/${day}`)
}

/**
 * Format relative time in Persian
 * @param {Date|string} date - The date to format
 * @returns {string} - Relative time in Persian
 */
export function formatPersianRelativeTime(date) {
  const now = new Date()
  const dateObj = new Date(date)
  const diffInSeconds = Math.floor((now - dateObj) / 1000)
  
  if (diffInSeconds < 60) {
    return 'همین الان'
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60)
    return `${toPersianNumerals(minutes)} دقیقه پیش`
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600)
    return `${toPersianNumerals(hours)} ساعت پیش`
  } else if (diffInSeconds < 2592000) {
    const days = Math.floor(diffInSeconds / 86400)
    return `${toPersianNumerals(days)} روز پیش`
  } else {
    return formatPersianDate(date)
  }
}

/**
 * Format large numbers with Persian separators
 * @param {number} num - The number to format
 * @returns {string} - Formatted number with Persian numerals and separators
 */
export function formatPersianNumber(num) {
  const formatted = num.toLocaleString('fa-IR')
  return toPersianNumerals(formatted)
}

/**
 * Truncate Persian text properly (considering RTL)
 * @param {string} text - The text to truncate
 * @param {number} maxLength - Maximum length
 * @returns {string} - Truncated text
 */
export function truncatePersianText(text, maxLength = 100) {
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

/**
 * Persian month names
 */
export const PERSIAN_MONTHS = [
  'فروردین', 'اردیبهشت', 'خرداد', 'تیر', 'مرداد', 'شهریور',
  'مهر', 'آبان', 'آذر', 'دی', 'بهمن', 'اسفند'
]

/**
 * Persian day names
 */
export const PERSIAN_DAYS = [
  'یکشنبه', 'دوشنبه', 'سه‌شنبه', 'چهارشنبه', 'پنج‌شنبه', 'جمعه', 'شنبه'
]

/**
 * Academic field translations
 */
export const ACADEMIC_FIELDS = {
  'technology': 'علوم و فناوری',
  'medicine': 'پزشکی',
  'social': 'علوم اجتماعی',
  'engineering': 'مهندسی',
  'physics': 'فیزیک',
  'chemistry': 'شیمی',
  'biology': 'زیست‌شناسی',
  'mathematics': 'ریاضیات',
  'psychology': 'روان‌شناسی',
  'philosophy': 'فلسفه',
  'literature': 'ادبیات',
  'history': 'تاریخ',
  'economics': 'اقتصاد',
  'law': 'حقوق',
  'education': 'علوم تربیتی'
}

/**
 * Get Persian field name
 * @param {string} fieldKey - The field key
 * @returns {string} - Persian field name
 */
export function getPersianFieldName(fieldKey) {
  return ACADEMIC_FIELDS[fieldKey] || fieldKey
}
