<template>
  <article 
    class="neo-card p-4 hover-lift transition-all duration-300 relative"
    :class="{ 'ring-2 ring-primary-200 bg-primary-50/30': news.isPinned }"
    dir="rtl"
  >
    <!-- Pinned Badge -->
    <div 
      v-if="news.isPinned"
      class="absolute top-2 left-2 bg-primary-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1"
    >
      <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd" />
      </svg>
      <span>مهم</span>
    </div>

    <!-- Header -->
    <div class="flex items-start justify-between gap-4 mb-3">
      <div class="flex-1 min-w-0">
        <!-- Category Badge -->
        <div class="mb-2">
          <span 
            class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
            :class="categoryClasses"
          >
            <component :is="categoryIcon" class="w-3 h-3 ml-1" />
            {{ categoryLabel }}
          </span>
        </div>

        <!-- Title -->
        <h3 class="text-lg font-semibold text-slate-900 leading-tight mb-2 text-right">
          <a 
            :href="`#/news/${news.id}`"
            class="hover:text-primary-600 focus-ring rounded-lg transition-colors"
            @click.prevent="$emit('view', news.id)"
          >
            {{ news.title }}
          </a>
        </h3>
      </div>

      <!-- Date -->
      <div class="flex-shrink-0 text-sm text-slate-500">
        <div class="flex items-center gap-1">
          <CalendarIcon class="w-4 h-4" />
          <span class="persian-date">{{ news.date }}</span>
        </div>
      </div>
    </div>

    <!-- Summary -->
    <div class="mb-4">
      <p class="text-sm text-slate-600 leading-relaxed text-right line-clamp-2">
        {{ news.summary }}
      </p>
    </div>

    <!-- Footer Actions -->
    <div class="flex items-center justify-between">
      <!-- Read More -->
      <button
        @click="$emit('view', news.id)"
        class="text-primary-600 hover:text-primary-700 text-sm font-medium focus-ring rounded-lg px-2 py-1 transition-colors"
      >
        ادامه مطلب
        <ChevronLeftIcon class="w-4 h-4 inline mr-1 transform rotate-180" />
      </button>

      <!-- Share Actions -->
      <div class="flex items-center gap-2">
        <!-- Share Button -->
        <button
          @click="$emit('share', news)"
          class="p-2 text-slate-400 hover:text-slate-600 focus-ring rounded-lg transition-colors"
          aria-label="اشتراک‌گذاری"
        >
          <ShareIcon class="w-4 h-4" />
        </button>

        <!-- Bookmark -->
        <button
          @click="toggleBookmark"
          class="p-2 transition-colors focus-ring rounded-lg"
          :class="isBookmarked ? 'text-primary-600' : 'text-slate-400 hover:text-slate-600'"
          :aria-label="isBookmarked ? 'حذف از نشان‌شده‌ها' : 'نشان‌گذاری'"
        >
          <BookmarkIcon 
            class="w-4 h-4" 
            :class="{ 'fill-current': isBookmarked }"
          />
        </button>
      </div>
    </div>

    <!-- Loading State -->
    <div 
      v-if="isLoading"
      class="absolute inset-0 bg-white/80 backdrop-blur-sm rounded-xl flex items-center justify-center"
    >
      <div class="w-6 h-6 border-2 border-primary-500 border-t-transparent rounded-full animate-spin"></div>
    </div>
  </article>
</template>

<script setup>
import { ref, computed } from 'vue'
import {
  CalendarIcon,
  ChevronLeftIcon,
  ShareIcon,
  BookmarkIcon,
  SpeakerphoneIcon,
  DocumentTextIcon,
  UserGroupIcon,
  CogIcon
} from '@heroicons/vue/24/outline'

// Props
const props = defineProps({
  news: {
    type: Object,
    required: true
  }
})

// Emits
const emit = defineEmits(['view', 'share', 'bookmark'])

// Reactive state
const isLoading = ref(false)
const isBookmarked = ref(false)

// Category configurations
const categoryConfig = {
  'announcement': {
    label: 'اطلاعیه',
    icon: SpeakerphoneIcon,
    classes: 'bg-blue-100 text-blue-700'
  },
  'call-for-papers': {
    label: 'فراخوان مقاله',
    icon: DocumentTextIcon,
    classes: 'bg-green-100 text-green-700'
  },
  'partnership': {
    label: 'همکاری',
    icon: UserGroupIcon,
    classes: 'bg-purple-100 text-purple-700'
  },
  'feature': {
    label: 'ویژگی جدید',
    icon: CogIcon,
    classes: 'bg-yellow-100 text-yellow-700'
  }
}

// Computed properties
const categoryLabel = computed(() => {
  return categoryConfig[props.news.category]?.label || 'عمومی'
})

const categoryIcon = computed(() => {
  return categoryConfig[props.news.category]?.icon || SpeakerphoneIcon
})

const categoryClasses = computed(() => {
  return categoryConfig[props.news.category]?.classes || 'bg-slate-100 text-slate-700'
})

// Methods
const toggleBookmark = async () => {
  isLoading.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 300)) // Simulate API call
    isBookmarked.value = !isBookmarked.value
    emit('bookmark', props.news.id)
  } finally {
    isLoading.value = false
  }
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
