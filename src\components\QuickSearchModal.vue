<template>
  <Dialog 
    :open="true" 
    @close="$emit('close')"
    class="relative z-50"
    dir="rtl"
  >
    <!-- Backdrop -->
    <div class="fixed inset-0 bg-black/30 backdrop-blur-sm" aria-hidden="true" />

    <!-- Modal Container -->
    <div class="fixed inset-0 flex items-start justify-center p-4 pt-16">
      <Dialog.Panel class="w-full max-w-2xl neo-surface rounded-2xl p-6 animate-slide-up">
        
        <!-- Header -->
        <div class="flex items-center justify-between mb-6">
          <Dialog.Title class="text-lg font-semibold text-slate-900 text-right">
            جستجوی سریع
          </Dialog.Title>
          <button
            @click="$emit('close')"
            class="p-2 text-slate-400 hover:text-slate-600 focus-ring rounded-lg"
            aria-label="بستن"
          >
            <XMarkIcon class="w-5 h-5" />
          </button>
        </div>

        <!-- Search Input -->
        <div class="relative mb-4">
          <input
            ref="searchInput"
            v-model="query"
            type="text"
            class="w-full neo-input rounded-xl py-4 px-6 text-right placeholder:text-slate-400"
            placeholder="جستجو در مقالات، نویسندگان، مجلات..."
            @keydown.enter="handleSearch"
            @keydown.escape="$emit('close')"
            dir="rtl"
          />
          <MagnifyingGlassIcon class="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-400" />
        </div>

        <!-- Quick Suggestions -->
        <div v-if="!query" class="space-y-3">
          <h3 class="text-sm font-medium text-slate-700 text-right">جستجوهای محبوب</h3>
          <div class="flex flex-wrap gap-2 justify-end">
            <button
              v-for="suggestion in popularSearches"
              :key="suggestion"
              @click="selectSuggestion(suggestion)"
              class="px-3 py-1 rounded-full bg-slate-100 text-slate-700 text-sm hover:bg-slate-200 transition-colors"
            >
              {{ suggestion }}
            </button>
          </div>
        </div>

        <!-- Search Results -->
        <div v-else-if="filteredResults.length > 0" class="space-y-2 max-h-64 overflow-y-auto">
          <button
            v-for="result in filteredResults"
            :key="result.id"
            @click="selectResult(result)"
            class="w-full p-3 text-right hover:bg-slate-50 rounded-lg transition-colors focus-ring"
          >
            <div class="flex items-center gap-3">
              <div class="flex-shrink-0">
                <DocumentTextIcon v-if="result.type === 'paper'" class="w-5 h-5 text-primary-500" />
                <UserIcon v-else-if="result.type === 'author'" class="w-5 h-5 text-success" />
                <BookOpenIcon v-else class="w-5 h-5 text-accent-500" />
              </div>
              <div class="flex-1 min-w-0 text-right">
                <div class="font-medium text-slate-900 truncate">{{ result.title }}</div>
                <div class="text-sm text-slate-500 truncate">{{ result.meta }}</div>
              </div>
            </div>
          </button>
        </div>

        <!-- No Results -->
        <div v-else-if="query" class="text-center py-8">
          <MagnifyingGlassIcon class="w-12 h-12 text-slate-300 mx-auto mb-3" />
          <p class="text-slate-500">نتیجه‌ای یافت نشد</p>
          <p class="text-sm text-slate-400 mt-1">کلمات کلیدی دیگری امتحان کنید</p>
        </div>

        <!-- Footer -->
        <div class="mt-6 pt-4 border-t border-slate-200 flex items-center justify-between text-sm text-slate-500">
          <div class="flex items-center gap-4">
            <span>Enter برای جستجو</span>
            <span>Esc برای بستن</span>
          </div>
          <button
            @click="openAdvancedSearch"
            class="text-primary-600 hover:text-primary-700 font-medium"
          >
            جستجوی پیشرفته
          </button>
        </div>
      </Dialog.Panel>
    </div>
  </Dialog>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue'
import { Dialog } from '@headlessui/vue'
import { 
  XMarkIcon, 
  MagnifyingGlassIcon,
  DocumentTextIcon,
  UserIcon,
  BookOpenIcon
} from '@heroicons/vue/24/outline'
import { searchSuggestions } from '@/data/mockData'

// Emits
const emit = defineEmits(['close', 'search', 'advancedSearch'])

// Reactive state
const query = ref('')
const searchInput = ref(null)

// Popular searches
const popularSearches = [
  'یادگیری عمیق',
  'هوش مصنوعی',
  'شبکه‌های عصبی',
  'پردازش تصویر',
  'داده‌کاوی',
  'بیوانفورماتیک'
]

// Computed properties
const filteredResults = computed(() => {
  if (!query.value) return []
  
  return searchSuggestions.filter(item =>
    item.title.includes(query.value) ||
    item.meta.includes(query.value)
  ).slice(0, 6)
})

// Methods
const handleSearch = () => {
  if (query.value.trim()) {
    emit('search', query.value)
  }
}

const selectSuggestion = (suggestion) => {
  query.value = suggestion
  handleSearch()
}

const selectResult = (result) => {
  emit('search', result.title)
}

const openAdvancedSearch = () => {
  emit('advancedSearch')
}

// Auto-focus on mount
onMounted(() => {
  nextTick(() => {
    if (searchInput.value) {
      searchInput.value.focus()
    }
  })
})
</script>
