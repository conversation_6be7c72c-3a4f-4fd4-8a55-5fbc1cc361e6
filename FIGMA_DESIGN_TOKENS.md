# ScholarHub - Figma Design Tokens Specification

## 🎨 Color Palette

### Primary Colors
```
Primary 50:  #eff6ff
Primary 100: #dbeafe  
Primary 200: #bfdbfe
Primary 300: #93c5fd
Primary 400: #60a5fa
Primary 500: #125CC8 (Main Brand)
Primary 600: #0f52b3
Primary 700: #0d479e
Primary 800: #0a3d89
Primary 900: #083374
```

### Accent Colors
```
Accent 50:  #f0f9ff
Accent 100: #e0f2fe
Accent 200: #bae6fd
Accent 300: #7dd3fc
Accent 400: #38bdf8
Accent 500: #0ea5e9 (Secondary Brand)
Accent 600: #0284c7
Accent 700: #0369a1
Accent 800: #075985
Accent 900: #0c4a6e
```

### Surface Colors
```
Surface 50:  #f8fafc (Page Background)
Surface 100: #f1f5f9
Surface 200: #e2e8f0
Surface 300: #cbd5e1
Surface 400: #94a3b8
Surface 500: #64748b
Surface 600: #475569 (Muted Text)
Surface 700: #334155
Surface 800: #1e293b
Surface 900: #0f172a
```

### Semantic Colors
```
Success: #10b981
Danger:  #ef4444
Warning: #f59e0b
White:   #ffffff
```

### Gradients
```
Primary Gradient: linear-gradient(135deg, #125CC8 0%, #0ea5e9 100%)
Accent Gradient:  linear-gradient(135deg, #0ea5e9 0%, #3b82f6 100%)
```

## 📝 Typography

### Font Families
```
Primary (Persian): 'Vazirmatn', 'Vazir', 'IRANSans', system-ui, sans-serif
Serif (Articles):  'Source Serif Pro', serif
Monospace:         'JetBrains Mono', monospace
```

### Font Weights
```
Light:    300
Regular:  400 (Body Text)
Medium:   500
Semibold: 600 (Headings)
Bold:     700 (CTAs)
```

### Text Styles

#### Persian Headings (RTL)
```
H1 Hero:
- Size: 40px / 48px (desktop), 32px / 40px (mobile)
- Weight: 600
- Line Height: 1.2
- Text Align: right

H2 Section:
- Size: 28px / 34px (desktop), 24px / 30px (mobile)  
- Weight: 600
- Line Height: 1.25
- Text Align: right

H3 Subsection:
- Size: 20px / 28px
- Weight: 600
- Line Height: 1.4
- Text Align: right
```

#### Body Text (RTL)
```
Body Large:
- Size: 18px / 28px
- Weight: 400
- Text Align: right

Body Regular:
- Size: 16px / 24px
- Weight: 400
- Text Align: right

Body Small:
- Size: 14px / 20px
- Weight: 400
- Text Align: right

Caption:
- Size: 12px / 16px
- Weight: 400
- Text Align: right
```

## 📏 Spacing & Layout

### Spacing Scale (8px baseline)
```
Space 1:  4px
Space 2:  8px
Space 3:  12px
Space 4:  16px
Space 5:  20px
Space 6:  24px
Space 8:  32px
Space 10: 40px
Space 12: 48px
Space 16: 64px
Space 20: 80px
Space 24: 96px
```

### Container Widths
```
Max Width: 1280px (7xl)
Padding:   24px (desktop), 16px (mobile)
```

### Grid System
```
Columns: 12
Gutter:  24px (desktop), 16px (mobile)
```

### Breakpoints
```
Mobile:  375px - 767px
Tablet:  768px - 1023px  
Desktop: 1024px+
Large:   1280px+
```

## 🎭 Shadows & Effects

### Neomorphism Shadows
```
Neo Small:
box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1), 
           -2px -2px 4px rgba(255, 255, 255, 0.8)

Neo Medium:
box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.1), 
           -4px -4px 8px rgba(255, 255, 255, 0.8)

Neo Large:
box-shadow: 6px 6px 12px rgba(0, 0, 0, 0.15), 
           -6px -6px 12px rgba(255, 255, 255, 0.9)

Neo Inset:
box-shadow: inset 2px 2px 4px rgba(0, 0, 0, 0.1), 
           inset -2px -2px 4px rgba(255, 255, 255, 0.8)
```

### Glass Effects
```
Glass Surface:
background: rgba(255, 255, 255, 0.6)
backdrop-filter: blur(8px)
border: 1px solid rgba(255, 255, 255, 0.2)
box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37)
```

## 🔘 Border Radius
```
Small:  8px  (buttons, chips)
Medium: 12px (cards)
Large:  16px (modals)
XLarge: 20px (hero sections)
Round:  50%  (avatars, icons)
```

## 🎯 Component Specifications

### Buttons

#### Primary Button
```
Background: Primary 500 (#125CC8)
Text: White
Padding: 12px 24px
Border Radius: 12px
Font Weight: 600
Shadow: Neo Medium
Hover: Primary 600 + Neo Large
```

#### Secondary Button  
```
Background: Neo Surface
Text: Surface 700
Padding: 12px 24px
Border Radius: 12px
Font Weight: 500
Shadow: Neo Medium
Hover: Neo Large
```

### Cards

#### Paper Card
```
Background: Neo Surface
Padding: 24px
Border Radius: 16px
Shadow: Neo Medium
Hover: Neo Large + translate(-2px)
```

#### News Item
```
Background: Neo Surface
Padding: 16px
Border Radius: 12px
Shadow: Neo Small
Hover: Neo Medium
```

### Form Elements

#### Search Input
```
Background: rgba(255, 255, 255, 0.4)
Backdrop Filter: blur(2px)
Border: 2px solid transparent
Border Radius: 12px
Padding: 16px 24px
Font Size: 18px
Focus: Border Primary 300 + Glow Animation
```

#### Filter Chips
```
Background: Surface 100
Text: Surface 700
Padding: 8px 16px
Border Radius: 20px
Font Size: 14px
Active: Primary 500 background + White text
```

## 📱 Mobile Specifications

### Bottom Navigation
```
Height: 64px + safe-area-inset-bottom
Background: rgba(255, 255, 255, 0.9)
Backdrop Filter: blur(12px)
Border Top: 1px solid Surface 200
```

### Mobile Breakpoints
```
Safe Area Top: env(safe-area-inset-top)
Safe Area Bottom: env(safe-area-inset-bottom)
```

## ♿ Accessibility Specifications

### Focus States
```
Focus Ring: 2px solid Primary 500
Focus Offset: 2px
Border Radius: 8px
```

### Color Contrast Ratios
```
Primary on White: 4.5:1 (AA)
Surface 600 on White: 4.5:1 (AA)
Surface 700 on White: 7:1 (AAA)
```

### Touch Targets
```
Minimum Size: 44px × 44px
Recommended: 48px × 48px
```

## 🎨 Icon Specifications

### Icon Sizes
```
Small:  16px (inline icons)
Medium: 20px (buttons)
Large:  24px (navigation)
XLarge: 32px (features)
```

### Icon Style
```
Stroke Width: 1.5px
Style: Outline (Heroicons)
RTL: Flip horizontally where appropriate
```

## 🌐 RTL Specifications

### Text Direction
```
Direction: rtl
Text Align: right
```

### Layout Mirroring
```
Flex Direction: row-reverse (where appropriate)
Margin/Padding: Use logical properties
Icons: Flip arrows, chevrons horizontally
```

### Persian Numerals
```
Western: 0123456789
Persian: ۰۱۲۳۴۵۶۷۸۹
```

## 📊 Chart Specifications

### Colors
```
Technology: #125CC8
Medicine:   #10b981  
Engineering: #f59e0b
Social:     #ef4444
Sciences:   #8b5cf6
Education:  #06b6d4
Other:      #64748b
```

### Chart Styles
```
Background: Neo Surface
Border Radius: 16px
Padding: 24px
Grid Lines: Surface 200
Text: Surface 600
```

## 🎭 Animation Specifications

### Durations
```
Fast:   150ms (hover states)
Normal: 300ms (transitions)
Slow:   500ms (page transitions)
```

### Easing
```
Ease Out: cubic-bezier(0, 0, 0.2, 1)
Ease In Out: cubic-bezier(0.4, 0, 0.2, 1)
```

### Keyframes
```
Fade In: opacity 0 → 1, translateY(10px) → 0
Slide Up: translateY(100%) → 0
Pulse Glow: box-shadow intensity variation
```

---

## 📋 Figma Setup Checklist

1. **Create Color Styles** for all color tokens
2. **Create Text Styles** for all typography variants  
3. **Create Effect Styles** for shadows and blurs
4. **Set up Grid Styles** for layout consistency
5. **Create Component Variants** for all interactive states
6. **Use Auto Layout** for responsive components
7. **Apply RTL constraints** for proper mirroring
8. **Test accessibility** with color contrast plugins
9. **Export assets** as SVG with proper naming
10. **Document component usage** with descriptions

This specification provides everything needed for a designer to create pixel-perfect Figma designs that match the implemented Vue.js components.
