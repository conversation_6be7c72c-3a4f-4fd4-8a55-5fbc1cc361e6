<template>
  <div dir="rtl" lang="fa" class="min-h-screen">
    
    <!-- Hero Section with Search -->
    <SearchHero 
      @search="$emit('search', $event)"
      @advanced-search="$emit('advancedSearch')"
      @category-search="$emit('categorySearch', $event)"
    />

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        
        <!-- Left Column - Main Content -->
        <div class="lg:col-span-2 space-y-8">
          
          <!-- Trending Papers Section -->
          <section class="animate-fade-in">
            <div class="flex items-center justify-between mb-6">
              <h2 class="text-persian-h2 text-slate-900">مقالات پرطرفدار</h2>
              <button class="text-primary-600 hover:text-primary-700 text-sm font-medium focus-ring rounded-lg px-3 py-1">
                مشاهده همه
                <ChevronLeftIcon class="w-4 h-4 inline mr-1 transform rotate-180" />
              </button>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <PaperCard
                v-for="paper in trendingPapers.slice(0, 6)"
                :key="paper.id"
                :paper="paper"
                @view="handlePaperAction('view', $event)"
                @download="handlePaperAction('download', $event)"
                @save="handlePaperAction('save', $event)"
                @like="handlePaperAction('like', $event)"
                @cite="handlePaperAction('cite', $event)"
                @share="handlePaperAction('share', $event)"
                @comments="handlePaperAction('comments', $event)"
                @author-click="handleAuthorClick"
                @request-access="handlePaperAction('requestAccess', $event)"
              />
            </div>
          </section>

          <!-- Load More Button -->
          <div class="text-center">
            <button 
              @click="loadMorePapers"
              :disabled="isLoadingMore"
              class="btn-secondary px-8 py-3 flex items-center gap-2 mx-auto"
            >
              <span v-if="!isLoadingMore">مشاهده مقالات بیشتر</span>
              <span v-else>در حال بارگذاری...</span>
              <div v-if="isLoadingMore" class="w-4 h-4 border-2 border-slate-400 border-t-transparent rounded-full animate-spin"></div>
            </button>
          </div>
        </div>

        <!-- Right Column - Sidebar -->
        <div class="space-y-8">
          
          <!-- Statistics Panel -->
          <section class="animate-fade-in" style="animation-delay: 0.2s;">
            <StatsPanel />
          </section>

          <!-- News and Announcements -->
          <section class="animate-fade-in" style="animation-delay: 0.4s;">
            <div class="flex items-center justify-between mb-6">
              <h2 class="text-xl font-semibold text-slate-900 text-right">اخبار و اطلاعیه‌ها</h2>
              <button class="text-primary-600 hover:text-primary-700 text-sm font-medium focus-ring rounded-lg px-3 py-1">
                همه اخبار
                <ChevronLeftIcon class="w-4 h-4 inline mr-1 transform rotate-180" />
              </button>
            </div>
            
            <div class="space-y-4">
              <NewsItem
                v-for="news in newsItems"
                :key="news.id"
                :news="news"
                @view="handleNewsAction('view', $event)"
                @share="handleNewsAction('share', $event)"
                @bookmark="handleNewsAction('bookmark', $event)"
              />
            </div>
          </section>

          <!-- Quick Links -->
          <section class="neo-card p-6 animate-fade-in" style="animation-delay: 0.6s;">
            <h3 class="text-lg font-semibold text-slate-900 mb-4 text-right">دسترسی سریع</h3>
            <div class="space-y-3">
              <a 
                href="#" 
                class="flex items-center gap-3 p-3 rounded-lg hover:bg-slate-50 transition-colors focus-ring text-right"
                @click.prevent="$emit('categorySearch', 'open-access')"
              >
                <div class="w-8 h-8 bg-success/10 rounded-lg flex items-center justify-center">
                  <LockOpenIcon class="w-4 h-4 text-success" />
                </div>
                <div class="flex-1">
                  <div class="font-medium text-slate-900">مقالات دسترسی آزاد</div>
                  <div class="text-sm text-slate-500">{{ formatPersianNumber(450000) }} مقاله</div>
                </div>
              </a>
              
              <a 
                href="#" 
                class="flex items-center gap-3 p-3 rounded-lg hover:bg-slate-50 transition-colors focus-ring text-right"
                @click.prevent="$emit('categorySearch', 'recent')"
              >
                <div class="w-8 h-8 bg-primary-500/10 rounded-lg flex items-center justify-center">
                  <ClockIcon class="w-4 h-4 text-primary-600" />
                </div>
                <div class="flex-1">
                  <div class="font-medium text-slate-900">مقالات اخیر</div>
                  <div class="text-sm text-slate-500">آخرین ۳۰ روز</div>
                </div>
              </a>
              
              <a 
                href="#" 
                class="flex items-center gap-3 p-3 rounded-lg hover:bg-slate-50 transition-colors focus-ring text-right"
                @click.prevent="handleTopAuthors"
              >
                <div class="w-8 h-8 bg-accent-500/10 rounded-lg flex items-center justify-center">
                  <UserIcon class="w-4 h-4 text-accent-600" />
                </div>
                <div class="flex-1">
                  <div class="font-medium text-slate-900">پژوهشگران برتر</div>
                  <div class="text-sm text-slate-500">بیشترین استناد</div>
                </div>
              </a>
            </div>
          </section>

          <!-- Research Tools -->
          <section class="neo-card p-6 animate-fade-in" style="animation-delay: 0.8s;">
            <h3 class="text-lg font-semibold text-slate-900 mb-4 text-right">ابزارهای پژوهشی</h3>
            <div class="space-y-3">
              <button class="w-full flex items-center gap-3 p-3 rounded-lg hover:bg-slate-50 transition-colors focus-ring text-right">
                <div class="w-8 h-8 bg-yellow-500/10 rounded-lg flex items-center justify-center">
                  <DocumentTextIcon class="w-4 h-4 text-yellow-600" />
                </div>
                <div class="flex-1">
                  <div class="font-medium text-slate-900">مولد استناد</div>
                  <div class="text-sm text-slate-500">APA, MLA, Chicago</div>
                </div>
              </button>
              
              <button class="w-full flex items-center gap-3 p-3 rounded-lg hover:bg-slate-50 transition-colors focus-ring text-right">
                <div class="w-8 h-8 bg-purple-500/10 rounded-lg flex items-center justify-center">
                  <ChartBarIcon class="w-4 h-4 text-purple-600" />
                </div>
                <div class="flex-1">
                  <div class="font-medium text-slate-900">تحلیل استناد</div>
                  <div class="text-sm text-slate-500">نمودار شبکه استناد</div>
                </div>
              </button>
              
              <button class="w-full flex items-center gap-3 p-3 rounded-lg hover:bg-slate-50 transition-colors focus-ring text-right">
                <div class="w-8 h-8 bg-green-500/10 rounded-lg flex items-center justify-center">
                  <BellIcon class="w-4 h-4 text-green-600" />
                </div>
                <div class="flex-1">
                  <div class="font-medium text-slate-900">هشدار مقاله</div>
                  <div class="text-sm text-slate-500">اطلاع از مقالات جدید</div>
                </div>
              </button>
            </div>
          </section>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <footer class="bg-slate-900 text-white py-12 mt-16" dir="rtl">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div class="md:col-span-2">
            <div class="flex items-center gap-3 mb-4">
              <div class="w-10 h-10 bg-primary-500 rounded-xl flex items-center justify-center">
                <BookOpenIcon class="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 class="text-xl font-bold">ScholarHub</h3>
                <p class="text-slate-400 text-sm">پایگاه داده مقالات علمی</p>
              </div>
            </div>
            <p class="text-slate-300 text-sm leading-relaxed max-w-md">
              بزرگ‌ترین پایگاه داده فارسی مقالات علمی و پژوهشی با هدف ارتقای دانش و تسهیل دسترسی پژوهشگران به منابع علمی معتبر.
            </p>
          </div>
          
          <div>
            <h4 class="font-semibold mb-4">دسترسی سریع</h4>
            <ul class="space-y-2 text-sm text-slate-300">
              <li><a href="#" class="hover:text-white transition-colors">جستجوی پیشرفته</a></li>
              <li><a href="#" class="hover:text-white transition-colors">مقالات دسترسی آزاد</a></li>
              <li><a href="#" class="hover:text-white transition-colors">پژوهشگران برتر</a></li>
              <li><a href="#" class="hover:text-white transition-colors">آمار و گزارش</a></li>
            </ul>
          </div>
          
          <div>
            <h4 class="font-semibold mb-4">پشتیبانی</h4>
            <ul class="space-y-2 text-sm text-slate-300">
              <li><a href="#" class="hover:text-white transition-colors">راهنمای استفاده</a></li>
              <li><a href="#" class="hover:text-white transition-colors">تماس با ما</a></li>
              <li><a href="#" class="hover:text-white transition-colors">سوالات متداول</a></li>
              <li><a href="#" class="hover:text-white transition-colors">گزارش مشکل</a></li>
            </ul>
          </div>
        </div>
        
        <div class="border-t border-slate-800 mt-8 pt-8 text-center text-sm text-slate-400">
          <p>© ۱۴۰۴ ScholarHub. تمامی حقوق محفوظ است.</p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import {
  ChevronLeftIcon,
  LockOpenIcon,
  ClockIcon,
  UserIcon,
  DocumentTextIcon,
  ChartBarIcon,
  BellIcon,
  BookOpenIcon
} from '@heroicons/vue/24/outline'

import SearchHero from './SearchHero.vue'
import PaperCard from './PaperCard.vue'
import StatsPanel from './StatsPanel.vue'
import NewsItem from './NewsItem.vue'

import { formatPersianNumber } from '@/utils/persian'
import { trendingPapers, newsItems } from '@/data/mockData'

// Emits
const emit = defineEmits([
  'search', 'advancedSearch', 'categorySearch', 
  'paperAction', 'newsAction'
])

// Reactive state
const isLoadingMore = ref(false)

// Methods
const handlePaperAction = (action, paperId, data) => {
  emit('paperAction', action, paperId, data)
}

const handleNewsAction = (action, newsId, data) => {
  emit('newsAction', action, newsId, data)
}

const handleAuthorClick = (authorName) => {
  emit('search', { query: authorName, type: 'author' })
}

const handleTopAuthors = () => {
  emit('categorySearch', 'top-authors')
}

const loadMorePapers = async () => {
  isLoadingMore.value = true
  
  // Simulate API call
  await new Promise(resolve => setTimeout(resolve, 1500))
  
  isLoadingMore.value = false
  
  // In a real app, you would load more papers here
  console.log('Loading more papers...')
}
</script>
