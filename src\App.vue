<template>
  <AppLayout
    :loading="isLoading"
    :loading-message="loadingMessage"
    @navigate="handleNavigation"
    @search="handleSearch"
    ref="layoutRef"
  >
    <HomePage 
      @search="handleSearch"
      @advanced-search="handleAdvancedSearch"
      @category-search="handleCategorySearch"
      @paper-action="handlePaperAction"
      @news-action="handleNewsAction"
    />
  </AppLayout>
</template>

<script setup>
import { ref } from 'vue'
import AppLayout from './components/AppLayout.vue'
import HomePage from './components/HomePage.vue'

// Reactive state
const isLoading = ref(false)
const loadingMessage = ref('در حال بارگذاری...')
const layoutRef = ref(null)

// Navigation handler
const handleNavigation = (tab) => {
  console.log('Navigate to:', tab)
  
  // Add toast notification for navigation
  if (layoutRef.value) {
    layoutRef.value.addToast({
      type: 'info',
      title: 'ناوبری',
      message: `به بخش ${getTabLabel(tab)} رفتید`
    })
  }
}

// Search handler
const handleSearch = (searchData) => {
  console.log('Search:', searchData)
  
  isLoading.value = true
  loadingMessage.value = 'در حال جستجو...'
  
  // Simulate search API call
  setTimeout(() => {
    isLoading.value = false
    
    if (layoutRef.value) {
      layoutRef.value.addToast({
        type: 'success',
        title: 'جستجو انجام شد',
        message: `نتایج برای "${typeof searchData === 'string' ? searchData : searchData.query}" یافت شد`
      })
    }
  }, 1500)
}

// Advanced search handler
const handleAdvancedSearch = () => {
  console.log('Open advanced search')
  
  if (layoutRef.value) {
    layoutRef.value.addToast({
      type: 'info',
      title: 'جستجوی پیشرفته',
      message: 'صفحه جستجوی پیشرفته باز می‌شود...'
    })
  }
}

// Category search handler
const handleCategorySearch = (categoryId) => {
  console.log('Search category:', categoryId)
  
  isLoading.value = true
  loadingMessage.value = 'در حال بارگذاری دسته‌بندی...'
  
  setTimeout(() => {
    isLoading.value = false
    
    if (layoutRef.value) {
      layoutRef.value.addToast({
        type: 'success',
        title: 'دسته‌بندی بارگذاری شد',
        message: 'مقالات این دسته نمایش داده شد'
      })
    }
  }, 1000)
}

// Paper action handler
const handlePaperAction = (action, paperId, data) => {
  console.log('Paper action:', action, paperId, data)
  
  const actionMessages = {
    view: 'مشاهده جزئیات مقاله',
    download: 'دانلود مقاله',
    save: 'ذخیره مقاله',
    like: 'پسندیدن مقاله',
    cite: 'دریافت استناد',
    share: 'اشتراک‌گذاری مقاله'
  }
  
  if (layoutRef.value) {
    layoutRef.value.addToast({
      type: action === 'download' ? 'success' : 'info',
      title: actionMessages[action] || 'عملیات انجام شد',
      message: action === 'download' ? 'دانلود شروع شد' : 'عملیات با موفقیت انجام شد'
    })
  }
}

// News action handler
const handleNewsAction = (action, newsId, data) => {
  console.log('News action:', action, newsId, data)
  
  if (layoutRef.value) {
    layoutRef.value.addToast({
      type: 'info',
      title: 'اخبار',
      message: 'عملیات با موفقیت انجام شد'
    })
  }
}

// Helper function to get tab labels
const getTabLabel = (tab) => {
  const labels = {
    home: 'خانه',
    search: 'جستجوی پیشرفته',
    profile: 'پروفایل'
  }
  return labels[tab] || tab
}
</script>
